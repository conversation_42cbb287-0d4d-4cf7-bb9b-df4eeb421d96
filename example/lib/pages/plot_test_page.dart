import 'package:flutter/material.dart';
import '../models/message.dart';
import '../models/chat_session.dart';
import '../utils/plot_parser.dart';
import '../test_data/sample_plot_responses.dart';
import '../widgets/message_widget.dart';
import '../ui/design_spec.dart';

/// Plot解析测试页面
class PlotTestPage extends StatefulWidget {
  const PlotTestPage({super.key});

  @override
  State<PlotTestPage> createState() => _PlotTestPageState();
}

class _PlotTestPageState extends State<PlotTestPage> {
  List<Message> _messages = [];
  int _currentSampleIndex = 0;
  bool _inputEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSample(0);
  }

  void _loadSample(int index) {
    final samples = SamplePlotResponses.getAllSamples();
    final descriptions = SamplePlotResponses.getSampleDescriptions();
    
    if (index >= 0 && index < samples.length) {
      final sample = samples[index];
      final description = descriptions[index];
      
      // 清空现有消息
      _messages.clear();
      
      // 添加描述消息
      _messages.add(Message.plotMessage(
        id: 'desc_$index',
        content: '测试样例 ${index + 1}: $description',
      ));
      
      // 解析样例并生成消息
      final parseResult = PlotParser.parseContent(sample);
      final generatedMessages = PlotParser.generateMessagesFromPlot(parseResult, 'sample_$index');
      _messages.addAll(generatedMessages);
      
      // 检查是否有选择消息来设置输入框状态
      final hasChoices = generatedMessages.any((msg) => msg.type == MessageType.choices);
      _inputEnabled = !hasChoices;
      
      setState(() {
        _currentSampleIndex = index;
      });
    }
  }

  void _onChoiceSelected(Message message, MessageChoice choice) {
    // 更新选择状态
    final updatedChoices = message.choices?.map((c) {
      return c.copyWith(isSelected: c.id == choice.id);
    }).toList();

    final updatedMessage = message.copyWith(
      choices: updatedChoices,
      selectedChoiceId: choice.id,
    );

    // 更新消息列表
    setState(() {
      final messageIndex = _messages.indexWhere((m) => m.id == message.id);
      if (messageIndex != -1) {
        _messages[messageIndex] = updatedMessage;
      }
    });

    // 添加用户选择的反馈消息
    final feedbackMessage = Message.textMessage(
      id: 'feedback_${DateTime.now().millisecondsSinceEpoch}',
      content: '你选择了：${choice.text}',
      sender: MessageSender.user,
    );

    setState(() {
      _messages.add(feedbackMessage);
      
      // 处理不同类型的选择
      if (choice.id == 3) {
        // 第三个选项：启用输入框
        _inputEnabled = true;
        _messages.add(Message.plotMessage(
          id: 'input_enabled_${DateTime.now().millisecondsSinceEpoch}',
          content: '输入框已启用，你现在可以自由输入消息了。',
        ));
      } else {
        // 前两个选项：显示选择结果
        _messages.add(Message.plotMessage(
          id: 'choice_result_${DateTime.now().millisecondsSinceEpoch}',
          content: '你选择了选项 ${choice.id}，系统将处理你的选择...',
        ));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final samples = SamplePlotResponses.getAllSamples();
    final descriptions = SamplePlotResponses.getSampleDescriptions();

    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        backgroundColor: DesignSpec.secondaryBackground,
        elevation: 1,
        title: const Text(
          'Plot解析测试',
          style: TextStyle(
            color: Colors.black87,
            fontSize: DesignSpec.fontSizeLg,
            fontWeight: DesignSpec.fontWeightSemiBold,
          ),
        ),
        actions: [
          PopupMenuButton<int>(
            icon: const Icon(Icons.list, color: Colors.black87),
            onSelected: _loadSample,
            itemBuilder: (context) {
              return descriptions.asMap().entries.map((entry) {
                final index = entry.key;
                final description = entry.value;
                return PopupMenuItem<int>(
                  value: index,
                  child: Text(
                    '样例 ${index + 1}: $description',
                    style: const TextStyle(fontSize: 12),
                  ),
                );
              }).toList();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 当前样例信息
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: DesignSpec.secondaryBackground,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '当前样例: ${_currentSampleIndex + 1}/${samples.length}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: DesignSpec.fontSizeSm,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  descriptions[_currentSampleIndex],
                  style: const TextStyle(
                    fontSize: DesignSpec.fontSizeXs,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    ElevatedButton(
                      onPressed: _currentSampleIndex > 0 
                          ? () => _loadSample(_currentSampleIndex - 1)
                          : null,
                      child: const Text('上一个'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _currentSampleIndex < samples.length - 1
                          ? () => _loadSample(_currentSampleIndex + 1)
                          : null,
                      child: const Text('下一个'),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      '输入框状态: ${_inputEnabled ? "启用" : "禁用"}',
                      style: TextStyle(
                        fontSize: DesignSpec.fontSizeXs,
                        color: _inputEnabled ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 消息列表
          Expanded(
            child: _messages.isEmpty
                ? const Center(
                    child: Text('没有消息'),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      return MessageWidget(
                        message: _messages[index],
                        onChoiceSelected: _onChoiceSelected,
                      );
                    },
                  ),
          ),
          // 输入区域（模拟）
          Container(
            decoration: BoxDecoration(
              color: DesignSpec.secondaryBackground,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: _inputEnabled 
                              ? DesignSpec.primaryBackground
                              : Colors.grey[200],
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: _inputEnabled 
                                ? Colors.grey[300]!
                                : Colors.grey[400]!,
                            width: 1,
                          ),
                        ),
                        child: TextField(
                          decoration: InputDecoration(
                            hintText: _inputEnabled ? '输入消息...' : '输入框已禁用',
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          enabled: _inputEnabled,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: _inputEnabled 
                            ? DesignSpec.primaryItemSelected
                            : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        onPressed: _inputEnabled ? () {
                          // 模拟发送消息
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('这是测试页面，消息发送功能已禁用')),
                          );
                        } : null,
                        icon: const Icon(
                          Icons.send,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
