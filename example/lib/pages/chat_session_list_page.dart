import 'package:flutter/material.dart';
import '../data/system_prompt.dart';
import '../models/chat_session.dart';
import '../models/message.dart';
import '../ui/design_spec.dart';
import 'chat_page.dart';

class ChatSessionListPage extends StatefulWidget {
  const ChatSessionListPage({super.key});

  @override
  State<ChatSessionListPage> createState() => _ChatSessionListPageState();
}

class _ChatSessionListPageState extends State<ChatSessionListPage> {
  List<ChatSession> _sessions = [];

  @override
  void initState() {
    super.initState();
    _loadMockData();
  }

  void _loadMockData() {
    // 创建一些模拟数据
    final now = DateTime.now();
    _sessions = [
      ChatSession(
        id: '1',
        title: 'AI故事助手',
        messages: [
          Message.plotMessage(
            id: 'plot1',
            content: '欢迎使用AI故事助手！我可以为您创作精彩的故事。',
            timestamp: now.subtract(const Duration(hours: 2)),
          ),
          Message.textMessage(
            id: 'msg1',
            content: '请为我创作一个关于勇敢小兔子的故事',
            sender: MessageSender.user,
            timestamp: now.subtract(const Duration(hours: 1)),
          ),
          Message.imageMessage(
            id: 'img1',
            imageUrl: 'https://picsum.photos/400/300?random=1',
            content: '从前有一只勇敢的小兔子...',
            timestamp: now.subtract(const Duration(minutes: 30)),
          ),
        ],
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
        unreadCount: 1,
      ),
      ChatSession(
        id: '2',
        title: '童话世界',
        messages: [
          Message.plotMessage(
            id: 'sys2',
            content: '进入神奇的童话世界',
            timestamp: now.subtract(const Duration(days: 2)),
          ),
          Message.textMessage(
            id: 'msg2',
            content: '今天想听什么故事呢？',
            sender: MessageSender.assistant,
            timestamp: now.subtract(const Duration(hours: 5)),
          ),
        ],
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(hours: 5)),
        unreadCount: 0,
      ),
      ChatSession(
        id: '3',
        title: '冒险故事集',
        messages: [
          Message.textMessage(
            id: 'msg3',
            content: '我想听一个冒险故事',
            sender: MessageSender.user,
            timestamp: now.subtract(const Duration(days: 3)),
          ),
        ],
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 3)),
        unreadCount: 0,
      ),
    ];
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSpec.primaryBackground,
      appBar: AppBar(
        backgroundColor: DesignSpec.primaryBackground,
        elevation: 0,
        title: const Text(
          '聊天',
          style: TextStyle(
            color: Colors.black87,
            fontSize: DesignSpec.fontSizeXl,
            fontWeight: DesignSpec.fontWeightSemiBold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: DesignSpec.primaryItemSelected),
            onPressed: _createNewSession,
          ),
        ],
      ),
      body: _sessions.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              itemCount: _sessions.length,
              itemBuilder: (context, index) {
                return _buildSessionItem(_sessions[index]);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无聊天记录',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '点击右上角 + 开始新的对话',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionItem(ChatSession session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: DesignSpec.secondaryBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          radius: 24,
          backgroundColor: DesignSpec.primaryItemSelected.withOpacity(0.1),
          backgroundImage: session.avatarUrl != null
              ? NetworkImage(session.avatarUrl!)
              : null,
          child: session.avatarUrl == null
              ? Icon(
                  Icons.chat,
                  color: DesignSpec.primaryItemSelected,
                  size: 20,
                )
              : null,
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                session.title,
                style: TextStyle(
                  fontSize: DesignSpec.fontSizeBase,
                  fontWeight: DesignSpec.fontWeightSemiBold,
                  color: Colors.black87,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              session.formattedTime,
              style: TextStyle(
                fontSize: DesignSpec.fontSizeXs,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  session.lastMessagePreview,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (session.unreadCount > 0)
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: DesignSpec.primaryItemSelected,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    session.unreadCount > 99 ? '99+' : session.unreadCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),
        onTap: () => _openChatPage(session),
      ),
    );
  }

  void _openChatPage(ChatSession session) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(session: session),
      ),
    ).then((_) {
      // 从聊天页面返回时，可以在这里更新会话数据
      setState(() {});
    });
  }

  void _createNewSession() {
    final newSession = ChatSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: '新对话',
      messages: [
        Message.plotMessage(
          id: 'sys_${DateTime.now().millisecondsSinceEpoch}',
          content: '您好！我是您的AI助手，有什么可以帮助您的吗？',
        ),
      ],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setState(() {
      _sessions.insert(0, newSession);
    });

    _openChatPage(newSession);
  }
}