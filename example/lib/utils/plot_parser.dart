import 'dart:convert';
import '../models/plot.dart';
import '../models/message.dart';

/// Plot解析器，用于解析AI返回的文本内容
class PlotParser {
  /// 解析AI返回的文本内容，尝试提取Plot结构
  static PlotParseResult parseContent(String content) {
    if (content.trim().isEmpty) {
      return PlotParseResult.textOnly(content);
    }

    // 尝试解析JSON格式
    final jsonResult = _tryParseJson(content);
    if (jsonResult != null) {
      return jsonResult;
    }

    // 尝试解析混合格式（JSON + 文本）
    final mixedResult = _tryParseMixed(content);
    if (mixedResult != null) {
      return mixedResult;
    }

    // 如果都无法解析，返回纯文本
    return PlotParseResult.textOnly(content);
  }

  /// 尝试解析纯JSON格式
  static PlotParseResult? _tryParseJson(String content) {
    try {
      final jsonData = jsonDecode(content.replaceAll("```", "").trim());
      if (jsonData is Map<String, dynamic>) {
        final plot = Plot.fromJson(jsonData);
        return PlotParseResult.fromPlot(plot);
      }
    } catch (e) {
      // JSON解析失败，继续尝试其他方式
    }
    return null;
  }

  /// 尝试解析混合格式（可能包含JSON块和普通文本）
  static PlotParseResult? _tryParseMixed(String content) {
    // 查找JSON块的模式
    final jsonPattern = RegExp(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}');
    final matches = jsonPattern.allMatches(content);
    
    for (final match in matches) {
      try {
        final jsonStr = match.group(0);
        if (jsonStr != null) {
          final jsonData = jsonDecode(jsonStr);
          if (jsonData is Map<String, dynamic>) {
            final plot = Plot.fromJson(jsonData);
            
            // 提取JSON之外的文本作为额外内容
            final beforeJson = content.substring(0, match.start).trim();
            final afterJson = content.substring(match.end).trim();
            final extraText = [beforeJson, afterJson]
                .where((text) => text.isNotEmpty)
                .join('\n')
                .trim();
            
            return PlotParseResult.fromPlot(plot, extraText: extraText);
          }
        }
      } catch (e) {
        // 继续尝试下一个匹配
        continue;
      }
    }
    return null;
  }

  /// 从Plot对象生成消息列表
  static List<Message> generateMessagesFromPlot(PlotParseResult parseResult, String baseId) {
    final messages = <Message>[];
    final timestamp = DateTime.now();
    int messageIndex = 0;

    // 添加额外文本消息（如果有）
    if (parseResult.extraText != null && parseResult.extraText!.isNotEmpty) {
      messages.add(Message.textMessage(
        id: '${baseId}_extra_${messageIndex++}',
        content: parseResult.extraText!,
        sender: MessageSender.assistant,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    final plot = parseResult.plot;
    if (plot == null) {
      // 如果没有Plot，添加纯文本消息
      if (parseResult.textContent.isNotEmpty) {
        messages.add(Message.textMessage(
          id: '${baseId}_text_${messageIndex++}',
          content: parseResult.textContent,
          sender: MessageSender.assistant,
          timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
        ));
      }
      return messages;
    }

    // 添加剧情描述消息
    if (plot.plotDescription?.content != null && plot.plotDescription!.content!.isNotEmpty) {
      messages.add(Message.plotMessage(
        id: '${baseId}_plot_${messageIndex++}',
        content: plot.plotDescription!.content!,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    // 添加对话消息
    if (plot.dialogue?.content != null && plot.dialogue!.content!.isNotEmpty) {
      String dialogueContent = plot.dialogue!.content!;
      
      // 如果有角色信息，添加到内容前面
      if (plot.dialogue!.character != null && plot.dialogue!.character!.isNotEmpty) {
        String characterInfo = plot.dialogue!.character!;
        if (plot.dialogue!.status != null && plot.dialogue!.status!.isNotEmpty) {
          characterInfo += '（${plot.dialogue!.status!}）';
        }
        dialogueContent = '$characterInfo：$dialogueContent';
      }

      messages.add(Message.textMessage(
        id: '${baseId}_dialogue_${messageIndex++}',
        content: dialogueContent,
        sender: MessageSender.assistant,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    // 添加选择消息
    if (plot.choices?.options != null && plot.choices!.options!.isNotEmpty) {
      final choices = plot.choices!.options!.map((option) {
        return MessageChoice(
          id: (option.id ?? 0).toInt(),
          text: option.text ?? '',
        );
      }).toList();

      messages.add(Message.choicesMessage(
        id: '${baseId}_choices_${messageIndex++}',
        content: '请选择你的行动：',
        choices: choices,
        timestamp: timestamp.add(Duration(milliseconds: messageIndex * 100)),
      ));
    }

    return messages;
  }
}

/// Plot解析结果
class PlotParseResult {
  final Plot? plot;
  final String textContent;
  final String? extraText;

  PlotParseResult({
    this.plot,
    required this.textContent,
    this.extraText,
  });

  factory PlotParseResult.fromPlot(Plot plot, {String? extraText}) {
    return PlotParseResult(
      plot: plot,
      textContent: '',
      extraText: extraText,
    );
  }

  factory PlotParseResult.textOnly(String text) {
    return PlotParseResult(
      plot: null,
      textContent: text,
    );
  }

  bool get hasPlot => plot != null;
  bool get hasText => textContent.isNotEmpty;
  bool get hasExtraText => extraText != null && extraText!.isNotEmpty;
}
