import '../models/story.dart';
import '../models/chat_session.dart';
import '../models/message.dart';

class StoryChatAdapter {
  /// 将Story对象转换为ChatSession
  static ChatSession createChatSessionFromStory(Story story, String locale) {
    final title = _getLocalizedString(story.title, locale);
    final background = _getLocalizedString(story.backgroundSetting, locale);
    final character = _getLocalizedString(story.characterSetting, locale);
    final plot = _getLocalizedString(story.currentPlot, locale);
    
    final initialMessage = Message.plotMessage(
      id: 'initial_${story.id}_${DateTime.now().millisecondsSinceEpoch}',
      content: '''$background

$character

$plot''',
    );
    
    return ChatSession(
      id: 'story_${story.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      avatarUrl: story.imageUrl,
      messages: [initialMessage],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
  
  /// 获取本地化的字符串
  static String _getLocalizedString(Map<String, String> localizedMap, String locale) {
    return localizedMap[locale] ?? localizedMap['zh'] ?? localizedMap['en'] ?? '';
  }
}